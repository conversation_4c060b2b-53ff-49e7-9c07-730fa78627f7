require('dotenv').config({
  path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev"
});

const mongoose = require('mongoose');
const fs = require('fs');
const csvParser = require('csv-parser');

// Import all required models
require('../build/src/models/Supply');
require('../build/src/models/Invoice');
require('../build/src/models/Supplier');
require('../build/src/models/Pack');
require('../build/src/models/PackFeature');
require('../build/src/models/Tenant');
require('../build/src/models/TenantSubscription');
require('../build/src/models/SubscriptionBill');
require('../build/src/models/SubscriptionBillDetails');
require('../build/src/models/Room');
require('../build/src/models/Specialty');
require('../build/src/models/Session');
require('../build/src/models/Timeoff');
require('../build/src/models/Country');
require('../build/src/models/CountryCity');
require('../build/src/models/Prescription');
require('../build/src/models/Radiograph');
require('../build/src/models/RadiographFamily');
require('../build/src/models/SuperAdmin');
require('../build/src/models/Biologie');
require('../build/src/models/Staff');
require('../build/src/models/Patient');
require('../build/src/models/User');
require('../build/src/models/Profile');
require('../build/src/models/Drug');
require('../build/src/models/DrugFamily');
require('../build/src/models/Hospital');

// Get models after they're loaded
const Drug = mongoose.model('Drug');
const DrugFamily = mongoose.model('DrugFamily');
const Hospital = mongoose.model('Hospital');

// Helper function to read CSV file with manual parsing
const readCSVFile = (filePath) => {
  return new Promise((resolve, reject) => {
    try {
      console.log('Reading CSV file with manual parsing...');

      // Read the entire file
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const lines = fileContent.split('\n');

      console.log(`  Total lines in file: ${lines.length}`);

      if (lines.length === 0) {
        return resolve([]);
      }

      // Get headers from first line
      const headers = lines[0].split('|').map(h => h.trim());
      console.log(`  Headers found: ${headers.length} columns`);

      const results = [];
      let processedRows = 0;
      let skippedRows = 0;

      // Process data lines
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) {
          skippedRows++;
          continue;
        }

        const values = line.split('|');

        // Create object from headers and values
        const row = {};
        headers.forEach((header, index) => {
          row[header] = values[index] ? values[index].trim() : '';
        });

        results.push(row);
        processedRows++;

        if (processedRows % 20000 === 0) {
          console.log(`  Processed ${processedRows} rows...`);
        }
      }

      console.log(`  Finished processing ${processedRows} rows (skipped ${skippedRows} empty lines)`);
      resolve(results);

    } catch (error) {
      console.error('Error reading CSV file:', error);
      reject(error);
    }
  });
};

// Helper function to find or create drug family
const findOrCreateDrugFamily = async (familyName, hospitalId) => {
  if (!familyName || familyName.trim() === '') {
    return null;
  }

  const trimmedName = familyName.trim();

  // Try to find existing drug family
  let drugFamily = await DrugFamily.findOne({
    name: { $regex: new RegExp(`^${trimmedName}$`, 'i') },
    hospital: hospitalId
  });

  if (!drugFamily) {
    // Create new drug family
    drugFamily = new DrugFamily({
      name: trimmedName,
      hospital: hospitalId
    });
    await drugFamily.save();
    console.log(`Created new drug family: ${trimmedName}`);
  }

  return drugFamily._id;
};

const syncDrugs = async () => {
  try {
    // Connect to MongoDB with modern options
    const url = process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office';
    console.log('Establishing connection with MongoDB at:', url);

    await mongoose.connect(url, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    console.log('Connected to MongoDB successfully');

    // Get the manager hospital
    const managerHospital = await Hospital.findOne({ isManager: true });
    if (!managerHospital) {
      throw new Error('Manager hospital not found');
    }
    console.log(`Found manager hospital: ${managerHospital.name || 'Unnamed'} (ID: ${managerHospital._id})`);

    // First, remove all existing drugs
    console.log('Removing all existing drugs...');
    const deleteResult = await Drug.deleteMany({});
    console.log(`Removed ${deleteResult.deletedCount} existing drugs`);

    // Read and parse CSV file
    console.log('Reading CSV file...');
    const csvData = await readCSVFile('prd.csv');
    console.log(`Total CSV rows read: ${csvData.length}`);

    // Filter data for allowed categories
    const allowedCategories = ['Médicament', 'Complement Alimentaire'];
    const filteredData = csvData.filter(row =>
      allowedCategories.includes(row.categorie_libelle)
    );

    console.log(`Filtered data: ${filteredData.length} rows (${allowedCategories.join(', ')})`);

    // Process drugs in batches
    const batchSize = 100;
    let processedCount = 0;
    let errorCount = 0;
    const drugFamilyCache = new Map();

    for (let i = 0; i < filteredData.length; i += batchSize) {
      const batch = filteredData.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(filteredData.length / batchSize)} (${batch.length} items)...`);

      const drugPromises = batch.map(async (row) => {
        try {
          // Handle drug family
          let drugFamilyId = null;
          if (row.classe_therapeutique) {
            const familyKey = row.classe_therapeutique.trim().toLowerCase();

            if (drugFamilyCache.has(familyKey)) {
              drugFamilyId = drugFamilyCache.get(familyKey);
            } else {
              drugFamilyId = await findOrCreateDrugFamily(row.classe_therapeutique, managerHospital._id);
              if (drugFamilyId) {
                drugFamilyCache.set(familyKey, drugFamilyId);
              }
            }
          }

          // Create drug data
          const drugData = {
            name: row.libelle_produit || row.designation || 'Unknown',
            code_winplus: row.code_produit_catalogue,
            drugFamily: drugFamilyId,
            price: parseFloat(row.prix_vente_ttc) || 0,
            hospital: managerHospital._id
          };

          // Validate required fields
          if (!drugData.code_winplus) {
            throw new Error('Missing code_winplus');
          }

          return drugData;
        } catch (error) {
          console.error(`Error processing row with code ${row.code_produit_catalogue}: ${error.message}`);
          errorCount++;
          return null;
        }
      });

      // Wait for all drugs in batch to be processed
      const batchResults = await Promise.all(drugPromises);
      const validDrugs = batchResults.filter(drug => drug !== null);

      // Insert valid drugs
      if (validDrugs.length > 0) {
        try {
          await Drug.insertMany(validDrugs, { ordered: false });
          processedCount += validDrugs.length;
          console.log(`  Inserted ${validDrugs.length} drugs (${processedCount}/${filteredData.length} total)`);
        } catch (insertError) {
          console.error(`Batch insert error: ${insertError.message}`);
          // Try individual inserts for this batch
          for (const drug of validDrugs) {
            try {
              await Drug.create(drug);
              processedCount++;
            } catch (individualError) {
              console.error(`Individual insert error for ${drug.code_winplus}: ${individualError.message}`);
              errorCount++;
            }
          }
        }
      }
    }

    // Final statistics
    const finalCount = await Drug.countDocuments();
    const drugFamilyCount = await DrugFamily.countDocuments({ hospital: managerHospital._id });

    console.log('\n=== SYNCHRONIZATION COMPLETE ===');
    console.log(`Total CSV rows: ${csvData.length}`);
    console.log(`Filtered rows: ${filteredData.length}`);
    console.log(`Successfully processed: ${processedCount}`);
    console.log(`Errors: ${errorCount}`);
    console.log(`Final drug count in database: ${finalCount}`);
    console.log(`Drug families created/found: ${drugFamilyCount}`);
    console.log(`Expected vs Actual: ${filteredData.length} vs ${finalCount}`);

    await mongoose.connection.close();
    console.log('Database connection closed');

  } catch (error) {
    console.error('Error during synchronization:', error);
    await mongoose.connection.close();
    process.exit(1);
  }
};

// Run the sync
if (require.main === module) {
  syncDrugs().catch(console.error);
}

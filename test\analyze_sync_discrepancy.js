const fs = require('fs');
const csvParser = require('csv-parser');

// Helper function to read CSV file
const readCSVFile = (filePath) => {
  return new Promise((resolve, reject) => {
    const results = [];
    
    fs.createReadStream(filePath)
      .pipe(csvParser({ 
        separator: '|',
        skipEmptyLines: true
      }))
      .on('data', (data) => {
        results.push(data);
      })
      .on('end', () => {
        resolve(results);
      })
      .on('error', (err) => {
        reject(err);
      });
  });
};

const analyzeDiscrepancy = async () => {
  try {
    console.log('Reading CSV file...');
    const csvData = await readCSVFile('prd.csv');
    console.log(`Total CSV rows read: ${csvData.length}`);

    // Count categories
    const categoryCounts = {};
    csvData.forEach(row => {
      const category = row.categorie_libelle || 'Unknown';
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });

    console.log('\nCategory distribution:');
    Object.entries(categoryCounts).forEach(([category, count]) => {
      console.log(`  ${category}: ${count}`);
    });

    // Filter for allowed categories
    const allowedCategories = ['Médicament', 'Complement Alimentaire'];
    const filteredData = csvData.filter(row => 
      allowedCategories.includes(row.categorie_libelle)
    );
    
    console.log(`\nFiltered data: ${filteredData.length} rows (${allowedCategories.join(', ')})`);
    console.log(`Expected from Python analysis: 20,561 (8,167 + 12,394)`);
    console.log(`Actual from Node.js: ${filteredData.length}`);

    // Check for missing data
    const medicamentCount = csvData.filter(row => row.categorie_libelle === 'Médicament').length;
    const complementCount = csvData.filter(row => row.categorie_libelle === 'Complement Alimentaire').length;
    
    console.log(`\nBreakdown:`);
    console.log(`  Médicament: ${medicamentCount}`);
    console.log(`  Complement Alimentaire: ${complementCount}`);
    console.log(`  Sum: ${medicamentCount + complementCount}`);

    // Check for duplicates by code_winplus
    const codeMap = new Map();
    let duplicates = 0;
    filteredData.forEach(row => {
      const code = row.code_produit_catalogue;
      if (codeMap.has(code)) {
        duplicates++;
        console.log(`Duplicate code found: ${code}`);
      } else {
        codeMap.set(code, true);
      }
    });

    console.log(`\nDuplicates found: ${duplicates}`);
    console.log(`Unique codes: ${codeMap.size}`);

  } catch (error) {
    console.error('Error during analysis:', error);
  }
};

analyzeDiscrepancy();

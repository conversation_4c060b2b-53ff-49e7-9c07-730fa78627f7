const fs = require('fs');
const csvParser = require('csv-parser');

let rowCount = 0;
let medicamentCount = 0;
let complementCount = 0;
let errorCount = 0;
const categoryCounts = {};

console.log('Starting CSV parsing debug...');

fs.createReadStream('prd.csv')
  .pipe(csvParser({ 
    separator: '|',
    skipEmptyLines: true
  }))
  .on('data', (data) => {
    rowCount++;
    
    if (rowCount % 10000 === 0) {
      console.log(`Processed ${rowCount} rows...`);
    }
    
    const category = data.categorie_libelle || 'Unknown';
    categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    
    if (category === 'Médicament') {
      medicamentCount++;
    } else if (category === 'Complement Alimentaire') {
      complementCount++;
    }
  })
  .on('error', (err) => {
    console.error('CSV parsing error:', err);
    errorCount++;
  })
  .on('end', () => {
    console.log('\n=== CSV PARSING COMPLETE ===');
    console.log(`Total rows processed: ${rowCount}`);
    console.log(`Médicament count: ${medicamentCount}`);
    console.log(`Complement Alimentaire count: ${complementCount}`);
    console.log(`Sum: ${medicamentCount + complementCount}`);
    console.log(`Errors: ${errorCount}`);
    
    console.log('\nTop 10 categories:');
    const sortedCategories = Object.entries(categoryCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);
    
    sortedCategories.forEach(([category, count]) => {
      console.log(`  ${category}: ${count}`);
    });
  });

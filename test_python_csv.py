import csv
from collections import Counter

file_path = 'prd.csv'

total_rows = 0
medicament_count = 0
complement_count = 0
categorie_counter = Counter()

with open(file_path, mode='r', encoding='utf-8') as file:
    reader = csv.DictReader(file, delimiter='|')
    for row in reader:
        total_rows += 1
        categorie = row.get('categorie_libelle', '').strip()

        # Count all values
        categorie_counter[categorie] += 1

        # Normalize to lowercase for comparison
        cat_lower = categorie.lower()

        if cat_lower == 'médicament':
            medicament_count += 1
        elif cat_lower == 'complement alimentaire':
            complement_count += 1

# Print stats
print(f"Total rows: {total_rows}")
print(f"Count of 'Médicament': {medicament_count}")
print(f"Count of 'Complement Alimentaire': {complement_count}")
print(f"Sum of both: {medicament_count + complement_count}")
print("\nAll categorie_libelle counts:")

# Print all category counts
for cat, count in categorie_counter.items():
    print(f"  {cat}: {count}")

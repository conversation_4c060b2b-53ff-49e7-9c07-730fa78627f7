require('dotenv').config({
  path: process.env.NODE_ENV
    ? `environments/.env.${process.env.NODE_ENV}`
    : 'environments/.env.dev',
});

const mongoose = require('mongoose');
const Drug = require('../src/models/Drug');
const DrugFamily = require('../src/models/DrugFamily');

const clearDrugs = async () => {
  try {
    // Connect to MongoDB
    const url = process.env.MONGODB_URI;
    console.log('Establishing connection with MongoDB at:', url);
    
    mongoose.Promise = global.Promise;
    mongoose.set('useNewUrlParser', true);
    mongoose.set('useFindAndModify', false);
    mongoose.set('useCreateIndex', true);
    mongoose.set('useUnifiedTopology', true);

    await mongoose.connect(url);
    console.log('Connected successfully to MongoDB');

    // Get counts before deletion
    const drugCount = await Drug.countDocuments({});
    const familyCount = await DrugFamily.countDocuments({});
    
    console.log('\nBefore deletion:');
    console.log(`Drugs in database: ${drugCount}`);
    console.log(`Drug families in database: ${familyCount}`);

    // Delete all drugs and drug families
    await Drug.deleteMany({});
    await DrugFamily.deleteMany({});

    // Verify deletion
    const newDrugCount = await Drug.countDocuments({});
    const newFamilyCount = await DrugFamily.countDocuments({});

    console.log('\nAfter deletion:');
    console.log(`Drugs in database: ${newDrugCount}`);
    console.log(`Drug families in database: ${newFamilyCount}`);
    
    console.log('\nDatabase cleared successfully!');
    
    await mongoose.disconnect();
    console.log('Database connection closed');

  } catch (error) {
    console.error('Error during database clearing:', error);
    process.exit(1);
  }
};

// Run the clear operation
clearDrugs();
